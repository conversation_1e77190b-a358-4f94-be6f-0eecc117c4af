'use client';

import { useState } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import type { Warehouse, WarehouseType } from '@/lib/types';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Edit, Trash2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

const initialFormState: Omit<Warehouse, 'id'> = {
  name: '',
  type: 'فرعي',
  location: '',
};

export default function WarehousesPage() {
  const {
    warehouses,
    addWarehouse,
    updateWarehouse,
    deleteWarehouse,
    checkWarehouseRelations,
    currentUser,
  } = useStore();
  const { toast } = useToast();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | null>(
    null,
  );
  const [warehouseToDelete, setWarehouseToDelete] = useState<Warehouse | null>(
    null,
  );
  const [formData, setFormData] =
    useState<Omit<Warehouse, 'id'>>(initialFormState);

  const permissions = currentUser?.permissions.warehouses;

  const handleOpenDialog = (warehouse: Warehouse | null) => {
    if (warehouse) {
      setEditingWarehouse(warehouse);
      setFormData(warehouse);
    } else {
      setEditingWarehouse(null);
      setFormData(initialFormState);
    }
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingWarehouse(null);
    setFormData(initialFormState);
  };

  const handleSave = () => {
    if (!formData.name || !formData.location) {
      toast({
        title: 'خطأ في الإدخال',
        description: 'يرجى ملء جميع الحقول.',
        variant: 'destructive',
      });
      return;
    }

    if (editingWarehouse) {
      updateWarehouse({ ...formData, id: editingWarehouse.id });
      toast({
        title: 'تم التحديث',
        description: 'تم تحديث بيانات المخزن بنجاح.',
      });
    } else {
      addWarehouse(formData);
      toast({ title: 'تمت الإضافة', description: 'تمت إضافة المخزن بنجاح.' });
    }
    handleDialogClose();
  };

  const handleDelete = () => {
    if (warehouseToDelete) {
      try {
        // فحص العلاقات قبل الحذف
        const relationCheck = checkWarehouseRelations(warehouseToDelete.id);
        if (!relationCheck.canDelete) {
          toast({
            variant: 'destructive',
            title: 'لا يمكن الحذف',
            description: relationCheck.reason +
              (relationCheck.relatedOperations ?
                '\nالعمليات المرتبطة: ' + relationCheck.relatedOperations.join(', ') :
                ''),
          });
          setWarehouseToDelete(null);
          return;
        }

        deleteWarehouse(warehouseToDelete.id);
        toast({
          title: 'تم الحذف',
          description: `تم حذف مخزن ${warehouseToDelete.name} بنجاح.`,
          variant: 'destructive',
        });
        setWarehouseToDelete(null);
      } catch (error) {
        toast({
          variant: 'destructive',
          title: 'خطأ في الحذف',
          description: error instanceof Error ? error.message : 'حدث خطأ غير متوقع',
        });
        setWarehouseToDelete(null);
      }
    }
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>إدارة المخازن</CardTitle>
          {permissions?.create && (
            <Button onClick={() => handleOpenDialog(null)}>
              إضافة مخزن جديد
            </Button>
          )}
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>اسم المخزن</TableHead>
                  <TableHead>النوع</TableHead>
                  <TableHead>الموقع</TableHead>
                  <TableHead>إجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {warehouses.map((warehouse) => (
                  <TableRow key={warehouse.id}>
                    <TableCell className="font-medium">
                      {warehouse.name}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          warehouse.type === 'رئيسي' ? 'default' : 'secondary'
                        }
                      >
                        {warehouse.type}
                      </Badge>
                    </TableCell>
                    <TableCell>{warehouse.location}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        {permissions?.edit && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleOpenDialog(warehouse)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        )}
                        {permissions?.delete && (
                          <Button
                            variant="ghost"
                            size="icon"
                            className="text-destructive hover:text-destructive"
                            onClick={() => setWarehouseToDelete(warehouse)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          className="sm:max-w-[425px]"
          onInteractOutside={(e) => e.preventDefault()}
          onCloseAutoFocus={handleDialogClose}
        >
          <DialogHeader>
            <DialogTitle>
              {editingWarehouse ? 'تعديل مخزن' : 'إضافة مخزن جديد'}
            </DialogTitle>
            <DialogDescription>
              {editingWarehouse
                ? 'قم بتحديث بيانات المخزن.'
                : 'أدخل بيانات المخزن الجديد هنا. انقر على "حفظ" عند الانتهاء.'}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                الاسم
              </Label>
              <Input
                id="name"
                placeholder="مثال: مخزن الفرع الشمالي"
                className="col-span-3"
                value={formData.name}
                onChange={(e) =>
                  setFormData({ ...formData, name: e.target.value })
                }
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                النوع
              </Label>
              <Select
                dir="rtl"
                value={formData.type}
                onValueChange={(value: WarehouseType) =>
                  setFormData({ ...formData, type: value })
                }
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="اختر النوع" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="رئيسي">رئيسي</SelectItem>
                  <SelectItem value="فرعي">فرعي</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="location" className="text-right">
                الموقع
              </Label>
              <Input
                id="location"
                placeholder="مثال: صنعاء - شارع تعز"
                className="col-span-3"
                value={formData.location}
                onChange={(e) =>
                  setFormData({ ...formData, location: e.target.value })
                }
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleSave}>حفظ</Button>
            <DialogClose asChild>
              <Button variant="outline">إلغاء</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        open={!!warehouseToDelete}
        onOpenChange={() => setWarehouseToDelete(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600">
              ⚠️ تأكيد حذف المخزن
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="space-y-2">
                <div className="text-gray-700">
                  هذا الإجراء لا يمكن التراجع عنه. سيؤدي هذا إلى حذف المخزن
                  <span className="font-semibold text-red-600">
                    {' "' + warehouseToDelete?.name + '"'}
                  </span>
                  بشكل دائم.
                </div>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mt-3">
                  <div className="text-yellow-800 text-sm">
                    <strong>تنبيه:</strong> سيتم فحص العلاقات المرتبطة قبل الحذف.
                    إذا كان للمخزن مبيعات، أوامر توريد، تحويلات مخزنية، أوامر تسليم، أو أجهزة، فلن يتم الحذف.
                  </div>
                </div>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-gray-100 hover:bg-gray-200">
              إلغاء
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              متابعة الحذف
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
