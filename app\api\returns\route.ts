import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, generateUniqueId } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    // استرجاع كل المرتجعات من قاعدة البيانات، مرتبة تنازلياً
    const returns = await prisma.return.findMany({
      orderBy: { id: 'desc' }
    });

    // تحويل حقول JSON من strings إلى objects
    const processedReturns = returns.map((returnRecord: any) => ({
      ...returnRecord,
      items: returnRecord.items ?
        (typeof returnRecord.items === 'string' ?
          JSON.parse(returnRecord.items) : returnRecord.items) : [],
      attachments: returnRecord.attachments ?
        (typeof returnRecord.attachments === 'string' ?
          JSON.parse(returnRecord.attachments) : returnRecord.attachments) : null,
    }));

    return NextResponse.json(processedReturns);
  } catch (error) {
    console.error('Failed to fetch returns:', error);
    return NextResponse.json({ error: 'Failed to fetch returns' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newReturn = await request.json();

    // Basic validation
    if (!newReturn.clientName || !newReturn.items || !Array.isArray(newReturn.items) || newReturn.items.length === 0) {
      return NextResponse.json(
        { error: 'Client name and items are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // استخدام رقم الأمر المُرسل من الواجهة الأمامية، أو إنشاء رقم جديد إذا لم يكن متوفراً
      let roNumber = newReturn.roNumber;
      if (!roNumber) {
        roNumber = await generateUniqueId(tx, 'return', 'RO-');
      } else {
        // التحقق من عدم وجود رقم مكرر
        const existingReturn = await tx.return.findUnique({
          where: { roNumber }
        });
        if (existingReturn) {
          roNumber = await generateUniqueId(tx, 'return', 'RO-');
        }
      }

      // إنشاء المرتجع في قاعدة البيانات
      const returnRecord = await tx.return.create({
        data: {
          roNumber,
          opReturnNumber: newReturn.opReturnNumber && newReturn.opReturnNumber.trim() !== '' ? newReturn.opReturnNumber : roNumber,
          date: newReturn.date,
          saleId: newReturn.saleId || null,
          soNumber: newReturn.soNumber || null,
          clientName: newReturn.clientName,
          warehouseName: newReturn.warehouseName || null,
          items: typeof newReturn.items === 'object' ? JSON.stringify(newReturn.items) : newReturn.items,
          notes: newReturn.notes || '',
          status: newReturn.status || 'معلق',
          processedBy: newReturn.processedBy || null,
          processedDate: newReturn.processedDate || null,
          employeeName: newReturn.employeeName || authResult.user!.username,
          attachments: newReturn.attachments ? JSON.stringify(newReturn.attachments) : null
        }
      });

      // تحديث حالة الأجهزة
      if (newReturn.items && Array.isArray(newReturn.items)) {
        for (const item of newReturn.items) {
          if (item.deviceId) {
            const device = await tx.device.findUnique({
              where: { id: item.deviceId }
            });

            if (device) {
              // تحديد الحالة الجديدة حسب سبب الإرجاع
              let newStatus = 'متاح للبيع';
              if (item.returnReason === 'خلل مصنعي') {
                newStatus = 'بانتظار إرسال للصيانة';
              } else if (item.returnReason === 'سبب آخر') {
                newStatus = 'بانتظار إرسال للصيانة';
              }

              await tx.device.update({
                where: { id: item.deviceId },
                data: { status: newStatus }
              });
            } else {
              console.warn(`Device ${item.deviceId} not found for return ${returnRecord.roNumber}`);
            }
          }
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created return: ${returnRecord.roNumber} for client ${returnRecord.clientName}`,
        tableName: 'return',
        recordId: returnRecord.id.toString()
      });

      return returnRecord;
    });

    // معالجة حقول JSON قبل الإرسال
    const processedReturn = {
      ...result,
      items: result.items ?
        (typeof result.items === 'string' ?
          JSON.parse(result.items) : result.items) : [],
      attachments: result.attachments ?
        (typeof result.attachments === 'string' ?
          JSON.parse(result.attachments) : result.attachments) : null,
    };

    return NextResponse.json(processedReturn, { status: 201 });
  } catch (error) {
    console.error('Failed to create return:', error);
    return NextResponse.json({ error: 'Failed to create return' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedReturn = await request.json();

    if (!updatedReturn.id) {
      return NextResponse.json(
        { error: 'Return ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود المرتجع
      const existingReturn = await tx.return.findUnique({
        where: { id: updatedReturn.id }
      });

      if (!existingReturn) {
        throw new Error('Return not found');
      }

      // تحديث المرتجع
      const returnRecord = await tx.return.update({
        where: { id: updatedReturn.id },
        data: {
          opReturnNumber: updatedReturn.opReturnNumber !== undefined ? updatedReturn.opReturnNumber : existingReturn.opReturnNumber,
          date: updatedReturn.date || existingReturn.date,
          saleId: updatedReturn.saleId !== undefined ? updatedReturn.saleId : existingReturn.saleId,
          soNumber: updatedReturn.soNumber !== undefined ? updatedReturn.soNumber : existingReturn.soNumber,
          clientName: updatedReturn.clientName || existingReturn.clientName,
          warehouseName: updatedReturn.warehouseName !== undefined ? updatedReturn.warehouseName : existingReturn.warehouseName,
          items: updatedReturn.items ? (typeof updatedReturn.items === 'object' ? JSON.stringify(updatedReturn.items) : updatedReturn.items) : existingReturn.items,
          notes: updatedReturn.notes !== undefined ? updatedReturn.notes : existingReturn.notes,
          status: updatedReturn.status || existingReturn.status,
          processedBy: updatedReturn.processedBy !== undefined ? updatedReturn.processedBy : existingReturn.processedBy,
          processedDate: updatedReturn.processedDate !== undefined ? updatedReturn.processedDate : existingReturn.processedDate,
          employeeName: updatedReturn.employeeName || existingReturn.employeeName,
          attachments: updatedReturn.attachments ? JSON.stringify(updatedReturn.attachments) : existingReturn.attachments
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated return: ${returnRecord.roNumber}`,
        tableName: 'return',
        recordId: returnRecord.id.toString()
      });

      return returnRecord;
    });

    // معالجة حقول JSON قبل الإرسال
    const processedReturn = {
      ...result,
      items: result.items ?
        (typeof result.items === 'string' ?
          JSON.parse(result.items) : result.items) : [],
      attachments: result.attachments ?
        (typeof result.attachments === 'string' ?
          JSON.parse(result.attachments) : result.attachments) : null,
    };

    return NextResponse.json(processedReturn);
  } catch (error) {
    console.error('Failed to update return:', error);

    if (error instanceof Error && error.message === 'Return not found') {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update return' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Return ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من وجود المرتجع
      const existingReturn = await tx.return.findUnique({
        where: { id }
      });

      if (!existingReturn) {
        throw new Error('Return not found');
      }

      // إرجاع حالة الأجهزة إلى "مباع" قبل الحذف
      let items = [];
      try {
        items = existingReturn.items ?
          (typeof existingReturn.items === 'string' ?
            JSON.parse(existingReturn.items) : existingReturn.items) : [];
      } catch (error) {
        console.warn('Failed to parse items for device status revert:', error);
      }

      if (Array.isArray(items)) {
        for (const item of items) {
          if (item.deviceId) {
            const device = await tx.device.findUnique({
              where: { id: item.deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: item.deviceId },
                data: { status: 'مباع' }
              });
            }
          }
        }
      }

      // حذف المرتجع
      await tx.return.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted return: ${existingReturn.roNumber}`,
        tableName: 'return',
        recordId: id.toString()
      });

      return { message: 'Return deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete return:', error);

    if (error instanceof Error && error.message === 'Return not found') {
      return NextResponse.json({ error: 'Return not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete return' }, { status: 500 });
  }
}
