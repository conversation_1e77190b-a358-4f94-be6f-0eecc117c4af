
'use client';

import { useState, useMemo } from 'react';
import { useStore } from '@/context/store';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Printer, FileDown, ShoppingCart, Undo, Repeat, Eye, ChevronsUpDown, Check, Calendar as CalendarIcon } from 'lucide-react';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { SystemSettings, Sale, Client, ReturnItem, Device, SaleItem } from '@/lib/types';
import { isAfter, subDays, subYears, format } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Calendar } from '@/components/ui/calendar';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/lib/utils';


const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
  const addHeader = () => {
    if (settings.logoUrl) {
      try {
        doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
      } catch (e) {
        console.error('Error adding logo image to PDF:', e);
      }
    }
    doc.setFontSize(16).text(settings.companyName, 190, 15, { align: 'right' });
    doc
      .setFontSize(10)
      .text(settings.companyAddress, 190, 22, { align: 'right' });
    doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
    doc.setLineWidth(0.5).line(15, 35, 195, 35);
  };
  const addFooter = (data: any) => {
    const pageCount = doc.internal.pages.length;
    doc
      .setFontSize(8)
      .text(
        `صفحة ${data.pageNumber} من ${pageCount - 1}`,
        data.settings.margin.left,
        doc.internal.pageSize.height - 10,
      );
    if (settings.reportFooter) {
      doc.text(settings.reportFooter, 195, doc.internal.pageSize.height - 10, {
        align: 'right',
      });
    }
  };
  return { addHeader, addFooter };
};

type DetailsModalData = {
  title: string;
  items: (SaleItem | ReturnItem | Device)[];
  headers: string[];
}

export default function ClientReports() {
  const { clients, sales, returns, systemSettings, devices } = useStore();
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [timeFilter, setTimeFilter] = useState<string>('all');
  const [customDateRange, setCustomDateRange] = useState<DateRange | undefined>();
  const [isClientSearchOpen, setIsClientSearchOpen] = useState(false);
  const [detailsModal, setDetailsModal] = useState<DetailsModalData | null>(null);

  const clientData = useMemo(() => {
    if (!selectedClient) return null;

    const client = clients.find(c => c.id.toString() === selectedClient);
    if (!client) return null;

    const getSinceDate = () => {
        if (timeFilter === 'all') return null;
        const now = new Date();
        if (timeFilter === '30') return subDays(now, 30);
        if (timeFilter === '180') return subDays(now, 180);
        if (timeFilter === '365') return subYears(now, 1);
        if (timeFilter === 'custom' && customDateRange?.from) return customDateRange.from;
        return null;
    };
    
    const getEndDate = () => {
        if(timeFilter === 'custom' && customDateRange?.to) return customDateRange.to;
        return new Date();
    }

    const sinceDate = getSinceDate();
    const endDate = getEndDate();

    const clientSales = sales.filter(s => {
      const saleDate = new Date(s.date);
      return s.clientName === client.name &&
             (!sinceDate || isAfter(saleDate, sinceDate)) &&
             (isAfter(endDate, saleDate));
    });

    const clientReturns = returns.filter(r => {
        const returnDate = new Date(r.date);
        return r.clientName === client.name &&
               (!sinceDate || isAfter(returnDate, sinceDate)) &&
               (isAfter(endDate, returnDate));
    });

    const purchaseCount = clientSales.reduce((acc, s) => acc + s.items.length, 0);
    const totalPurchaseValue = clientSales.reduce((acc, s) => acc + s.items.reduce((itemAcc, item) => itemAcc + item.price, 0), 0);
    
    const returnItems = clientReturns.flatMap(r => r.items.filter(item => !item.isReplacement));
    const replacementItems = clientReturns.flatMap(r => r.items.filter(item => item.isReplacement));
    
    const modelCounts = clientSales
      .flatMap(s => s.items)
      .reduce((acc, item) => {
        acc[item.model] = (acc[item.model] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    const topModels = Object.entries(modelCounts)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 5);
      
    const invoices: Sale[] = clientSales.sort((a,b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    return { client, purchaseCount, totalPurchaseValue, returnCount: returnItems.length, replacementCount: replacementItems.length, topModels, invoices, returnItems, replacementItems, soldItems: clientSales.flatMap(s => s.items) };
  }, [selectedClient, timeFilter, customDateRange, clients, sales, returns]);

  const handleExport = (action: 'print' | 'download') => {
    if (!clientData || !selectedClient) return;
    
    const { client, invoices } = clientData;
    const doc = new jsPDF();
    doc.setR2L(true);

    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
    addHeader();

    const title = `كشف حساب العميل: ${client.name}`;
    doc.setFontSize(20).text(title, 190, 45, { align: 'right' });

    autoTable(doc, {
      startY: 55,
      head: [['الإجمالي', 'عدد الأجهزة', 'التاريخ', 'رقم الفاتورة']],
      body: invoices.map(inv => [
        `$${inv.items.reduce((sum, i) => sum + i.price, 0).toFixed(2)}`,
        inv.items.length,
        new Date(inv.date).toLocaleDateString('ar-EG'),
        inv.soNumber,
      ]),
      theme: 'grid',
      styles: { font: 'Helvetica', halign: 'right' },
      headStyles: { halign: 'center', fillColor: [44, 51, 51] },
      didDrawPage: addFooter,
    });
    
    if (action === 'print') {
        doc.output('dataurlnewwindow');
    } else {
        doc.save(`client_statement_${client.name.replace(/\s+/g, '_')}.pdf`);
    }
  };
  
  const openDetails = (title: string, items: (SaleItem | ReturnItem | Device)[], headers: string[]) => {
      setDetailsModal({ title, items, headers });
  };

  return (
    <>
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>فلاتر التقرير</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <Label>اختيار العميل</Label>
            <Popover open={isClientSearchOpen} onOpenChange={setIsClientSearchOpen}>
              <PopoverTrigger asChild>
                <Button variant="outline" role="combobox" aria-expanded={isClientSearchOpen} className="w-full justify-between">
                  {selectedClient ? clients.find(c => c.id.toString() === selectedClient)?.name : "اختر عميلاً..."}
                  <ChevronsUpDown className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
                <Command>
                  <CommandInput placeholder="بحث عن عميل..." />
                  <CommandList>
                    <CommandEmpty>لا يوجد عميل بهذا الاسم.</CommandEmpty>
                    <CommandGroup>
                      {clients.map(c => (
                        <CommandItem key={c.id} value={c.name} onSelect={() => { setSelectedClient(c.id.toString()); setIsClientSearchOpen(false); }}>
                          <Check className={cn("ml-2 h-4 w-4", selectedClient === c.id.toString() ? "opacity-100" : "opacity-0")} />
                          {c.name}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>
          <div className="space-y-2">
            <Label>الفترة الزمنية</Label>
            <Select dir="rtl" value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger><SelectValue /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الأوقات</SelectItem>
                <SelectItem value="30">آخر 30 يوم</SelectItem>
                <SelectItem value="180">آخر 6 أشهر</SelectItem>
                <SelectItem value="365">آخر سنة</SelectItem>
                <SelectItem value="custom">تاريخ مخصص</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {timeFilter === 'custom' && (
            <div className="space-y-2">
                <Label>تحديد التاريخ</Label>
                <Popover>
                    <PopoverTrigger asChild>
                    <Button
                        id="date"
                        variant={"outline"}
                        className={cn("w-full justify-start text-left font-normal", !customDateRange && "text-muted-foreground")}
                    >
                        <CalendarIcon className="ml-2 h-4 w-4" />
                        {customDateRange?.from ? (
                        customDateRange.to ? (
                            <>
                            {format(customDateRange.from, "LLL dd, y")} -{" "}
                            {format(customDateRange.to, "LLL dd, y")}
                            </>
                        ) : (
                            format(customDateRange.from, "LLL dd, y")
                        )
                        ) : (
                        <span>اختر فترة</span>
                        )}
                    </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                        initialFocus
                        mode="range"
                        defaultMonth={customDateRange?.from}
                        selected={customDateRange}
                        onSelect={setCustomDateRange}
                        numberOfMonths={2}
                    />
                    </PopoverContent>
                </Popover>
            </div>
          )}
        </CardContent>
      </Card>

      {clientData ? (
        <>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card className="cursor-pointer hover:bg-muted/50" onClick={() => openDetails('الأجهزة المشتراة', clientData.soldItems, ['الموديل', 'السعر', 'الحالة'])}>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">إجمالي المشتريات</CardTitle>
                <ShoppingCart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{clientData.purchaseCount}</div>
                <p className="text-xs text-muted-foreground">
                  إجمالي القيمة: ${clientData.totalPurchaseValue.toFixed(2)}
                </p>
              </CardContent>
            </Card>
            <Card className="cursor-pointer hover:bg-muted/50" onClick={() => openDetails('الأجهزة المرتجعة', clientData.returnItems, ['الموديل', 'سبب الإرجاع'])}>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">إجمالي المرتجعات</CardTitle>
                <Undo className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{clientData.returnCount}</div>
              </CardContent>
            </Card>
            <Card className="cursor-pointer hover:bg-muted/50" onClick={() => openDetails('الأجهزة المستبدلة', clientData.replacementItems, ['الموديل (البديل)', 'الجهاز الأصلي'])}>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">إجمالي الاستبدالات</CardTitle>
                <Repeat className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{clientData.replacementCount}</div>
              </CardContent>
            </Card>
            <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                    <CardTitle className="text-sm font-medium">معدل الإرجاع</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="text-2xl font-bold">
                        {clientData.purchaseCount > 0 ? 
                            ((clientData.returnCount / clientData.purchaseCount) * 100).toFixed(1) : 0
                        }%
                    </div>
                </CardContent>
            </Card>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>أكثر الموديلات طلباً</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>الموديل</TableHead>
                      <TableHead className="text-center">الكمية</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clientData.topModels.map(([model, count]) => (
                      <TableRow key={model}>
                        <TableCell>{model}</TableCell>
                        <TableCell className="text-center">{count}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>كشف حساب العميل</CardTitle>
                <CardDescription>عرض لجميع فواتير العميل المحددة.</CardDescription>
              </CardHeader>
              <CardContent className="max-h-96 overflow-y-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>رقم الفاتورة</TableHead>
                      <TableHead>التاريخ</TableHead>
                      <TableHead>عدد الأجهزة</TableHead>
                      <TableHead>الإجمالي</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {clientData.invoices.map((invoice) => (
                      <TableRow key={invoice.id}>
                        <TableCell>{invoice.soNumber}</TableCell>
                        <TableCell>{new Date(invoice.date).toLocaleDateString('ar-EG')}</TableCell>
                        <TableCell>{invoice.items.length}</TableCell>
                        <TableCell>
                          ${invoice.items.reduce((sum, item) => sum + item.price, 0).toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="flex gap-2">
                <Button variant="outline" onClick={() => handleExport('print')}>
                  <Printer className="ml-2 h-4 w-4" /> طباعة
                </Button>
                <Button variant="outline" onClick={() => handleExport('download')}>
                  <FileDown className="ml-2 h-4 w-4" /> تصدير PDF
                </Button>
              </CardFooter>
            </Card>
          </div>
        </>
      ) : (
        <Card>
            <CardContent className="p-6 text-center text-muted-foreground">
                يرجى اختيار عميل لعرض تقريره المفصل.
            </CardContent>
        </Card>
      )}
    </div>
    
    <Dialog open={!!detailsModal} onOpenChange={() => setDetailsModal(null)}>
        <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
                <DialogTitle>{detailsModal?.title}</DialogTitle>
                <DialogDescription>
                    قائمة تفصيلية بالأجهزة في هذه الفئة للعميل المحدد.
                </DialogDescription>
            </DialogHeader>
            <ScrollArea className="max-h-[60vh] pr-4">
            <Table>
                <TableHeader>
                    <TableRow>
                        {detailsModal?.headers.map(header => <TableHead key={header}>{header}</TableHead>)}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {detailsModal?.items.length === 0 ? (
                        <TableRow>
                            <TableCell colSpan={detailsModal.headers.length} className="h-24 text-center">
                                لا توجد بيانات لعرضها.
                            </TableCell>
                        </TableRow>
                    ) : (
                        detailsModal?.items.map((item, index) => (
                            <TableRow key={(item as any).deviceId || index}>
                                <TableCell>{(item as any).model}</TableCell>
                                <TableCell>
                                    {'price' in item ? `$${item.price.toFixed(2)}` : 'returnReason' in item ? item.returnReason : (item as any).originalDeviceId || '-'}
                                </TableCell>
                                {'condition' in item && <TableCell>{item.condition}</TableCell>}
                            </TableRow>
                        ))
                    )}
                </TableBody>
            </Table>
            </ScrollArea>
            <DialogFooter>
                <DialogClose asChild>
                    <Button variant="outline">إغلاق</Button>
                </DialogClose>
            </DialogFooter>
        </DialogContent>
    </Dialog>
    </>
  );
}
