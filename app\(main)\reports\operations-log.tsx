'use client';

import { useState, useMemo } from 'react';
import { useStore } from '@/context/store';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { format, subDays, subYears } from 'date-fns';
import { Printer, FileDown } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { SystemSettings } from '@/lib/types';

const getPdfHeaderFooter = (doc: jsPDF, settings: SystemSettings) => {
  const addHeader = () => {
    if (settings.logoUrl) {
      try {
        doc.addImage(settings.logoUrl, 'PNG', 15, 10, 20, 20);
      } catch (e) {
        console.error('Error adding logo image to PDF:', e);
      }
    }
    doc.setFontSize(16).text(settings.companyName, 190, 15, { align: 'right' });
    doc
      .setFontSize(10)
      .text(settings.companyAddress, 190, 22, { align: 'right' });
    doc.text(settings.contactNumbers, 190, 29, { align: 'right' });
    doc.setLineWidth(0.5).line(15, 35, 195, 35);
  };
  const addFooter = (data: any) => {
    const pageCount = doc.internal.pages.length;
    doc
      .setFontSize(8)
      .text(
        `صفحة ${data.pageNumber} من ${pageCount - 1}`,
        data.settings.margin.left,
        doc.internal.pageSize.height - 10,
      );
    if (settings.reportFooter) {
      doc.text(settings.reportFooter, 195, doc.internal.pageSize.height - 10, {
        align: 'right',
      });
    }
  };
  return { addHeader, addFooter };
};

export default function OperationsLog() {
  const {
    sales = [],
    returns = [],
    supplyOrders = [],
    evaluationOrders = [],
    warehouseTransfers = [],
    maintenanceHistory = [],
    users = [],
    systemSettings,
  } = useStore();

  const [logType, setLogType] = useState('all');
  const [timeFilter, setTimeFilter] = useState('all');
  const [employeeFilter, setEmployeeFilter] = useState('all');
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [detailsData, setDetailsData] = useState<any>(null);

  const getEmployeeName = (log: any): string => {
    if (log.employeeName) return log.employeeName;
    if (log.username) return log.username;
    // Add more specific logic if needed, e.g., fetching from users array by id
    return 'غير محدد';
  };

  const logs = useMemo(() => {
    const sinceDate = (() => {
      if (timeFilter === 'all') return null;
      const now = new Date();
      if (timeFilter === '30') return subDays(now, 30);
      if (timeFilter === '180') return subDays(now, 180);
      if (timeFilter === '365') return subYears(now, 1);
      return null;
    })();

    const allLogs = [
      ...sales.map((s) => ({
        type: 'فاتورة مبيعات',
        id: s.soNumber,
        date: s.date,
        employeeName: 'غير محدد', // Sales don't have an employee name in the provided type
        details: `للعميل: ${s.clientName}, عدد الأجهزة: ${s.items.length}`,
        original: s,
      })),
      ...returns.map((r) => ({
        type: 'مرتجع',
        id: r.roNumber,
        date: r.date,
        employeeName: 'غير محدد', // Returns don't have an employee name
        details: `من العميل: ${r.clientName}, عدد الأجهزة: ${r.items.length}`,
        original: r,
      })),
      ...supplyOrders.map((so) => ({
        type: 'توريد',
        id: so.supplyOrderId,
        date: so.supplyDate,
        employeeName: so.employeeName,
        details: `من المورد: ${so.supplierId}, عدد الأجهزة: ${so.items.length}`,
        original: so,
      })),
      ...evaluationOrders.map((eo) => ({
        type: 'تقييم',
        id: eo.orderId,
        date: eo.date,
        employeeName: eo.employeeName,
        details: `بواسطة: ${eo.employeeName}, عدد الأجهزة: ${eo.items.length}`,
        original: eo,
      })),
      ...warehouseTransfers.map((wt) => ({
        type: 'نقل مخزني',
        id: wt.transferNumber,
        date: wt.date,
        employeeName: wt.employeeName,
        details: `من ${wt.fromWarehouseName} إلى ${wt.toWarehouseName}, عدد الأجهزة: ${wt.items.length}`,
        original: wt,
      })),
      ...maintenanceHistory.map((ml) => ({
        type: 'صيانة',
        id: ml.deviceId,
        date: ml.repairDate,
        employeeName: 'غير محدد', // Maintenance logs don't have an employee name
        details: `جهاز موديل ${ml.model}, النتيجة: ${ml.result}`,
        original: ml,
      })),
    ]
      .filter((log) => !sinceDate || new Date(log.date) > sinceDate)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    let filtered = allLogs;
    if (logType !== 'all') {
      filtered = filtered.filter((log) => log.type === logType);
    }
    if (employeeFilter !== 'all') {
      filtered = filtered.filter(
        (log) => getEmployeeName(log) === employeeFilter,
      );
    }

    return filtered;
  }, [
    logType,
    timeFilter,
    employeeFilter,
    sales,
    returns,
    supplyOrders,
    evaluationOrders,
    warehouseTransfers,
    maintenanceHistory,
  ]);

  const employeeOptions = useMemo(() => {
    const names = new Set<string>();
    sales.forEach((s) => {}); // No employee data
    returns.forEach((r) => {}); // No employee data
    supplyOrders.forEach((so) => names.add(so.employeeName));
    evaluationOrders.forEach((eo) => names.add(eo.employeeName));
    warehouseTransfers.forEach((wt) => names.add(wt.employeeName));
    return ['all', ...Array.from(names)];
  }, [sales, returns, supplyOrders, evaluationOrders, warehouseTransfers]);

  const handleOpenDetails = (log: any) => {
    setDetailsData(log);
    setIsDetailsOpen(true);
  };

  const handleGeneratePdf = (action: 'print' | 'download') => {
    if (!detailsData) return;
    const doc = new jsPDF();
    doc.setR2L(true);

    const { addHeader, addFooter } = getPdfHeaderFooter(doc, systemSettings);
    addHeader();

    const title = `تفاصيل العملية: ${detailsData.id}`;
    doc.setFontSize(20);
    doc.text(title, 190, 45, { align: 'right' });

    const employeeName = getEmployeeName(detailsData.original);

    const body = [
      ['النوع', detailsData.type],
      ['المعرف', detailsData.id],
      ['التاريخ', format(new Date(detailsData.date), 'yyyy-MM-dd')],
      ['الموظف', employeeName],
      ['التفاصيل', detailsData.details],
    ];

    autoTable(doc, {
      startY: 55,
      body: body,
      theme: 'grid',
      styles: { font: 'Helvetica', halign: 'right' },
    });

    if (action === 'print') {
      doc.output('dataurlnewwindow');
    } else {
      doc.save(`operation_${detailsData.id}.pdf`);
    }
  };

  return (
    <>
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>تفاصيل العملية</DialogTitle>
          </DialogHeader>
          {detailsData && (
            <div>
              <p>
                <strong>النوع:</strong> {detailsData.type}
              </p>
              <p>
                <strong>المعرف:</strong> {detailsData.id}
              </p>
              <p>
                <strong>التاريخ:</strong>{' '}
                {format(new Date(detailsData.date), 'yyyy-MM-dd')}
              </p>
              <p>
                <strong>الموظف:</strong> {getEmployeeName(detailsData.original)}
              </p>
              <p>
                <strong>التفاصيل:</strong> {detailsData.details}
              </p>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => handleGeneratePdf('print')}
            >
              <Printer className="ml-2 h-4 w-4" /> طباعة
            </Button>
            <Button
              variant="outline"
              onClick={() => handleGeneratePdf('download')}
            >
              <FileDown className="ml-2 h-4 w-4" /> تصدير
            </Button>
            <DialogClose asChild>
              <Button variant="outline">إغلاق</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Card>
        <CardHeader>
          <CardTitle>سجل العمليات</CardTitle>
          <CardDescription>
            عرض لجميع العمليات المسجلة في النظام.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Select dir="rtl" value={logType} onValueChange={setLogType}>
              <SelectTrigger>
                <SelectValue placeholder="اختر النوع..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الأنواع</SelectItem>
                <SelectItem value="فاتورة مبيعات">فواتير المبيعات</SelectItem>
                <SelectItem value="مرتجع">مرتجعات</SelectItem>
                <SelectItem value="توريد">توريد</SelectItem>
                <SelectItem value="تقييم">تقييم</SelectItem>
                <SelectItem value="نقل مخزني">نقل مخزني</SelectItem>
                <SelectItem value="صيانة">صيانة</SelectItem>
              </SelectContent>
            </Select>
            <Select dir="rtl" value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="اختر التوقيت..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الأوقات</SelectItem>
                <SelectItem value="30">آخر 30 يوم</SelectItem>
                <SelectItem value="180">آخر 6 أشهر</SelectItem>
                <SelectItem value="365">آخر سنة</SelectItem>
              </SelectContent>
            </Select>
            <Select
              dir="rtl"
              value={employeeFilter}
              onValueChange={setEmployeeFilter}
            >
              <SelectTrigger>
                <SelectValue placeholder="اختر الموظف..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">كل الموظفين</SelectItem>
                {employeeOptions.map((name) => (
                  <SelectItem key={name} value={name}>
                    {name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <ScrollArea className="h-[60vh]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>النوع</TableHead>
                  <TableHead>المعرف</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>الموظف</TableHead>
                  <TableHead>التفاصيل</TableHead>
                  <TableHead>عرض</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {logs.map((log, index) => (
                  <TableRow key={`${log.id}-${index}`}>
                    <TableCell>
                      <Badge variant="outline">{log.type}</Badge>
                    </TableCell>
                    <TableCell>{log.id}</TableCell>
                    <TableCell>
                      {format(new Date(log.date), 'yyyy-MM-dd')}
                    </TableCell>
                    <TableCell>{getEmployeeName(log.original)}</TableCell>
                    <TableCell>{log.details}</TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        onClick={() => handleOpenDetails(log)}
                      >
                        عرض
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
    </>
  );
}
