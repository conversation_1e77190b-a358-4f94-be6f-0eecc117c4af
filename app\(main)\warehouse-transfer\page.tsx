
'use client';

import { useState, useMemo, useRef } from 'react';
import { useStore } from '@/context/store';
import { useToast } from '@/hooks/use-toast';
import type {
  Warehouse,
  Device,
  WarehouseTransfer,
  WarehouseTransferItem,
  User,
} from '@/lib/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  PackagePlus,
  Trash2,
  Save,
  X,
  Upload,
  ArrowLeft,
  Send,
  PackageCheck,
  ScanLine,
  CheckSquare,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Checkbox } from '@/components/ui/checkbox';

// Mock current user - replace with actual user from auth context
const mockCurrentUser: User = {
  id: 1,
  name: 'مدير النظام',
  username: 'admin',
  email: '<EMAIL>',
  permissions: {
    warehouses: {
      view: true,
      create: true,
      edit: true,
      delete: true,
      manage: [1, 2, 3],
    }, // Example: has access to warehouses 1, 2, 3
    returns: {
      view: true,
      create: true,
      edit: true,
      delete: true,
      acceptWithoutWarranty: true,
    },
  },
};

const initialFormState = {
  fromWarehouseId: '',
  toWarehouseId: '',
  notes: '',
  employeeName: mockCurrentUser.name,
};

export default function WarehouseTransferPage() {
  const {
    warehouses,
    devices,
    warehouseTransfers,
    addWarehouseTransfer,
    updateDeviceWarehouse,
    completeWarehouseTransfer,
  } = useStore();
  const { toast } = useToast();

  const [formState, setFormState] = useState(initialFormState);
  const [itemsToTransfer, setItemsToTransfer] = useState<Device[]>([]);
  const [imeiInput, setImeiInput] = useState('');
  const [directTransfer, setDirectTransfer] = useState(false);
  const [receivingTransfer, setReceivingTransfer] =
    useState<WarehouseTransfer | null>(null);
  const [scannedImeis, setScannedImeis] = useState<Set<string>>(new Set());
  const [receiveImeiInput, setReceiveImeiInput] = useState('');

  const fileInputRef = useRef<HTMLInputElement>(null);

  const fromWarehouse = useMemo(
    () => warehouses.find((w) => w.id.toString() === formState.fromWarehouseId),
    [formState.fromWarehouseId, warehouses],
  );
  const toWarehouses = useMemo(
    () =>
      warehouses.filter((w) => w.id.toString() !== formState.fromWarehouseId),
    [formState.fromWarehouseId, warehouses],
  );

  const canDirectTransfer = useMemo(() => {
    if (!formState.fromWarehouseId || !formState.toWarehouseId) return false;
    const fromId = parseInt(formState.fromWarehouseId, 10);
    const toId = parseInt(formState.toWarehouseId, 10);
    const userWarehousePermissions =
      mockCurrentUser.permissions.warehouses?.manage || [];
    return (
      userWarehousePermissions.includes(fromId) &&
      userWarehousePermissions.includes(toId)
    );
  }, [formState.fromWarehouseId, formState.toWarehouseId]);

  const handleAddItem = () => {
    if (!fromWarehouse) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'يرجى تحديد المخزن المصدر أولاً.',
      });
      return;
    }
    if (!imeiInput) return;

    const device = devices.find((d) => d.id === imeiInput);
    if (!device) {
      toast({
        variant: 'destructive',
        title: 'جهاز غير موجود',
        description: 'هذا الرقم التسلسلي غير مسجل.',
      });
      return;
    }
    if (device.warehouseId !== fromWarehouse.id) {
      toast({
        variant: 'destructive',
        title: 'مخزن غير صحيح',
        description: 'الجهاز ليس في المخزن المصدر المحدد.',
      });
      return;
    }
    if (device.status !== 'متاح للبيع') {
      toast({
        variant: 'destructive',
        title: 'حالة غير صالحة',
        description: `لا يمكن نقل جهاز حالته '${device.status}'.`,
      });
      return;
    }
    if (itemsToTransfer.some((item) => item.id === imeiInput)) {
      toast({
        variant: 'destructive',
        title: 'مكرر',
        description: 'الجهاز مضاف بالفعل لقائمة النقل.',
      });
      return;
    }

    setItemsToTransfer((prev) => [...prev, device]);
    setImeiInput('');
  };

  const handleFileImport = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    if (!fromWarehouse) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'يرجى تحديد المخزن المصدر أولاً.',
      });
      return;
    }
    const file = event.target.files?.[0];
    if (!file) return;

    const text = await file.text();
    const lines = text
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    let addedCount = 0;
    let invalidCount = 0;
    let duplicateCount = 0;
    const newItems: Device[] = [];
    const existingDeviceIds = new Set(itemsToTransfer.map((item) => item.id));

    for (const imei of lines) {
      if (existingDeviceIds.has(imei)) {
        duplicateCount++;
        continue;
      }
      const device = devices.find((d) => d.id === imei);
      if (
        !device ||
        device.warehouseId !== fromWarehouse.id ||
        device.status !== 'متاح للبيع'
      ) {
        invalidCount++;
        continue;
      }
      newItems.push(device);
      existingDeviceIds.add(imei);
      addedCount++;
    }

    if (newItems.length > 0) {
      setItemsToTransfer((prev) => [...prev, ...newItems]);
    }

    toast({
      title: 'اكتمل الاستيراد',
      description: `تمت إضافة ${addedCount} جهازًا. تم تخطي ${invalidCount + duplicateCount} جهازًا (غير صالح أو مكرر).`,
    });

    if (event.target) event.target.value = '';
  };

  const handleRemoveItem = (deviceId: string) => {
    setItemsToTransfer((prev) => prev.filter((item) => item.id !== deviceId));
  };

  const handleCreateTransfer = () => {
    if (!formState.fromWarehouseId || !formState.toWarehouseId) {
      toast({
        variant: 'destructive',
        title: 'بيانات غير مكتملة',
        description: 'يرجى تحديد المخزن المصدر والمخزن الهدف.',
      });
      return;
    }
    if (itemsToTransfer.length === 0) {
      toast({
        variant: 'destructive',
        title: 'قائمة فارغة',
        description: 'يرجى إضافة جهاز واحد على الأقل.',
      });
      return;
    }

    const fromWh = warehouses.find(
      (w) => w.id.toString() === formState.fromWarehouseId,
    );
    const toWh = warehouses.find(
      (w) => w.id.toString() === formState.toWarehouseId,
    );

    if (!fromWh || !toWh) return;

    const transferData = {
      date: new Date().toISOString(),
      fromWarehouseId: fromWh.id,
      fromWarehouseName: fromWh.name,
      toWarehouseId: toWh.id,
      toWarehouseName: toWh.name,
      items: itemsToTransfer.map((d) => ({ deviceId: d.id, model: d.model })),
      employeeName: formState.employeeName,
      notes: formState.notes,
    };

    if (directTransfer && canDirectTransfer) {
      itemsToTransfer.forEach((item) => {
        updateDeviceWarehouse(item.id, toWh.id);
      });
      addWarehouseTransfer({ ...transferData, status: 'completed' });
      toast({
        title: 'تم التحويل المباشر بنجاح',
        description: `تم نقل ${itemsToTransfer.length} أجهزة مباشرة إلى ${toWh.name}.`,
        duration: 3000,
      });
    } else {
      addWarehouseTransfer(transferData);
      toast({
        title: 'تم إنشاء أمر التحويل',
        description: `تم إرسال ${itemsToTransfer.length} أجهزة بنجاح.`,
        duration: 3000,
      });
    }

    setFormState(initialFormState);
    setItemsToTransfer([]);
    setDirectTransfer(false);
  };

  const handleScanReceiveItem = () => {
    if (!receivingTransfer || !receiveImeiInput) return;

    const itemExistsInTransfer = receivingTransfer.items.some(
      (item) => item.deviceId === receiveImeiInput,
    );
    if (!itemExistsInTransfer) {
      toast({
        variant: 'destructive',
        title: 'جهاز غير صحيح',
        description: 'هذا الرقم التسلسلي لا ينتمي لأمر التحويل الحالي.',
      });
      setReceiveImeiInput('');
      return;
    }

    if (scannedImeis.has(receiveImeiInput)) {
      toast({
        variant: 'destructive',
        title: 'مكرر',
        description: 'تم استلام هذا الجهاز بالفعل.',
      });
      setReceiveImeiInput('');
      return;
    }

    setScannedImeis((prev) => new Set(prev).add(receiveImeiInput));
    setReceiveImeiInput('');
  };

  const handleCompleteReception = () => {
    if (!receivingTransfer) return;
    completeWarehouseTransfer(receivingTransfer.id);
    toast({
      title: 'اكتمل الاستلام',
      description: 'تم تحديث مواقع الأجهزة في المخزون.',
    });
    setReceivingTransfer(null);
    setScannedImeis(new Set());
  };

  if (receivingTransfer) {
    const itemsToReceiveList = receivingTransfer.items.filter(
      (item) => !scannedImeis.has(item.deviceId),
    );
    const itemsReceivedList = receivingTransfer.items.filter((item) =>
      scannedImeis.has(item.deviceId),
    );

    return (
      <div className="space-y-4 animate-in fade-in-50">
        <Button
          variant="outline"
          onClick={() => {
            setReceivingTransfer(null);
            setScannedImeis(new Set());
          }}
        >
          <ArrowLeft className="ml-2 h-4 w-4" /> الرجوع إلى قائمة التحويلات
        </Button>
        <Card>
          <CardHeader>
            <CardTitle>
              استلام أمر التحويل: {receivingTransfer.transferNumber}
            </CardTitle>
            <CardDescription>
              من:{' '}
              <Badge variant="secondary">
                {receivingTransfer.fromWarehouseName}
              </Badge>{' '}
              إلى:{' '}
              <Badge variant="secondary">
                {receivingTransfer.toWarehouseName}
              </Badge>
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex w-full max-w-sm items-center space-x-2 space-x-reverse">
              <Input
                placeholder="امسح أو أدخل الرقم التسلسلي..."
                value={receiveImeiInput}
                onChange={(e) => setReceiveImeiInput(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleScanReceiveItem()}
              />
              <Button onClick={handleScanReceiveItem}>
                <ScanLine className="ml-2 h-4 w-4" /> استلام
              </Button>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>
                    أجهزة بانتظار الاستلام ({itemsToReceiveList.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>الموديل</TableHead>
                        <TableHead>الرقم التسلسلي</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {itemsToReceiveList.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={2} className="text-center h-24">
                            تم استلام كل الأجهزة.
                          </TableCell>
                        </TableRow>
                      ) : (
                        itemsToReceiveList.map((item) => (
                          <TableRow key={item.deviceId}>
                            <TableCell>{item.model}</TableCell>
                            <TableCell dir="ltr">{item.deviceId}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>
                    أجهزة تم استلامها ({itemsReceivedList.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>الموديل</TableHead>
                        <TableHead>الرقم التسلسلي</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {itemsReceivedList.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={2} className="text-center h-24">
                            لم يتم استلام أي جهاز بعد.
                          </TableCell>
                        </TableRow>
                      ) : (
                        itemsReceivedList.map((item) => (
                          <TableRow
                            key={item.deviceId}
                            className="bg-green-500/10"
                          >
                            <TableCell>{item.model}</TableCell>
                            <TableCell dir="ltr">{item.deviceId}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              onClick={handleCompleteReception}
              disabled={scannedImeis.size !== receivingTransfer.items.length}
            >
              تأكيد استلام الدفعة كاملة ({scannedImeis.size}/
              {receivingTransfer.items.length})
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <Tabs defaultValue="create" className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="create">
          <Send className="ml-2 h-4 w-4" /> إنشاء أمر تحويل
        </TabsTrigger>
        <TabsTrigger value="receive">
          <PackageCheck className="ml-2 h-4 w-4" /> استلام التحويلات
        </TabsTrigger>
      </TabsList>

      <TabsContent value="create" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>1. تفاصيل أمر التحويل</CardTitle>
          </CardHeader>
          <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label>من مخزن (المصدر)</Label>
              <Select
                dir="rtl"
                onValueChange={(val) =>
                  setFormState({ ...initialFormState, fromWarehouseId: val })
                }
                value={formState.fromWarehouseId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر المخزن المصدر" />
                </SelectTrigger>
                <SelectContent>
                  {warehouses.map((w) => (
                    <SelectItem key={w.id} value={w.id.toString()}>
                      {w.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>إلى مخزن (الهدف)</Label>
              <Select
                dir="rtl"
                onValueChange={(val) =>
                  setFormState((s) => ({ ...s, toWarehouseId: val }))
                }
                value={formState.toWarehouseId}
                disabled={!formState.fromWarehouseId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="اختر المخزن الهدف" />
                </SelectTrigger>
                <SelectContent>
                  {toWarehouses.map((w) => (
                    <SelectItem key={w.id} value={w.id.toString()}>
                      {w.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>الموظف المسؤول</Label>
              <Input value={formState.employeeName} disabled />
            </div>
            <div className="space-y-2 md:col-span-full">
              <Label>ملاحظات</Label>
              <Textarea
                placeholder="ملاحظات إضافية على أمر التحويل..."
                value={formState.notes}
                onChange={(e) =>
                  setFormState((s) => ({ ...s, notes: e.target.value }))
                }
              />
            </div>
            {canDirectTransfer && (
              <div className="flex items-center space-x-2 space-x-reverse md:col-span-full">
                <Checkbox
                  id="direct-transfer"
                  checked={directTransfer}
                  onCheckedChange={(checked) =>
                    setDirectTransfer(checked as boolean)
                  }
                />
                <Label htmlFor="direct-transfer" className="cursor-pointer">
                  تنفيذ التحويل بشكل مباشر (لديك صلاحية على كلا المخزنين)
                </Label>
              </div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle>2. إضافة أجهزة للأمر</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-end gap-2">
              <div className="flex-grow space-y-2">
                <Label htmlFor="imei-input">
                  إدخال IMEI يدوي أو مسح باركود
                </Label>
                <Input
                  id="imei-input"
                  placeholder="أدخل الرقم التسلسلي..."
                  value={imeiInput}
                  onChange={(e) => setImeiInput(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleAddItem()}
                />
              </div>
              <Button onClick={handleAddItem}>إضافة</Button>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileImport}
                accept=".txt"
              />
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
              >
                <Upload className="ml-2 h-4 w-4" /> استيراد
              </Button>
            </div>

            <div className="rounded-lg border max-h-96 overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>الموديل</TableHead>
                    <TableHead>الرقم التسلسلي</TableHead>
                    <TableHead>إجراء</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {itemsToTransfer.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="h-24 text-center">
                        لم يتم إضافة أي أجهزة بعد.
                      </TableCell>
                    </TableRow>
                  ) : (
                    itemsToTransfer.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>{item.model}</TableCell>
                        <TableCell dir="ltr">{item.id}</TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveItem(item.id)}
                          >
                            <Trash2 className="h-4 w-4 text-destructive" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
          <CardFooter className="flex-wrap justify-start gap-2">
            <Button
              onClick={handleCreateTransfer}
              disabled={itemsToTransfer.length === 0}
            >
              <Send className="ml-2 h-4 w-4" />
              {directTransfer && canDirectTransfer
                ? 'تنفيذ التحويل المباشر'
                : 'إرسال أمر التحويل'}
              ({itemsToTransfer.length})
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                setFormState(initialFormState);
                setItemsToTransfer([]);
              }}
            >
              <X className="ml-2 h-4 w-4" /> إلغاء
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>

      <TabsContent value="receive">
        <Card>
          <CardHeader>
            <CardTitle>أوامر التحويل المعلقة</CardTitle>
            <CardDescription>
              قائمة بأوامر التحويل التي تم إرسالها وتنتظر تأكيد الاستلام في
              المخزن الهدف.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الأمر</TableHead>
                  <TableHead>من مخزن</TableHead>
                  <TableHead>إلى مخزن</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>عدد الأجهزة</TableHead>
                  <TableHead>إجراء</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(() => {
                  const pendingTransfers = warehouseTransfers.filter(
                    (t) => t.status === 'pending',
                  );
                  if (pendingTransfers.length === 0) {
                    return (
                      <TableRow>
                        <TableCell colSpan={6} className="h-24 text-center">
                          لا توجد أوامر تحويل معلقة حاليًا.
                        </TableCell>
                      </TableRow>
                    );
                  }
                  return pendingTransfers.map((transfer) => (
                    <TableRow key={transfer.id}>
                      <TableCell>{transfer.transferNumber}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {transfer.fromWarehouseName}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {transfer.toWarehouseName}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {format(new Date(transfer.date), 'yyyy-MM-dd')}
                      </TableCell>
                      <TableCell>{transfer.items.length}</TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          onClick={() => setReceivingTransfer(transfer)}
                        >
                          استلام
                        </Button>
                      </TableCell>
                    </TableRow>
                  ));
                })()}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}

    