import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, checkRelationsBeforeDelete } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const warehouses = await prisma.warehouse.findMany();
    return NextResponse.json(warehouses);
  } catch (error) {
    console.error('Failed to fetch warehouses:', error);
    return NextResponse.json({ error: 'Failed to fetch warehouses' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newWarehouse = await request.json();

    // Basic validation
    if (!newWarehouse.name || !newWarehouse.type) {
      return NextResponse.json(
        { message: 'Warehouse name and type are required' },
        { status: 400 },
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // التحقق من عدم وجود مستودع بنفس الاسم
      const existingWarehouse = await tx.warehouse.findFirst({
        where: { name: newWarehouse.name }
      });

      if (existingWarehouse) {
        throw new Error('Warehouse name already exists');
      }

      // Create new warehouse
      const warehouse = await tx.warehouse.create({
        data: {
          name: newWarehouse.name,
          type: newWarehouse.type,
          location: newWarehouse.location || '',
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created warehouse: ${warehouse.name}`
      });

      return warehouse;
    });

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error('Failed to create warehouse:', error);

    if (error instanceof Error && error.message === 'Warehouse name already exists') {
      return NextResponse.json({ error: 'Warehouse name already exists' }, { status: 409 });
    }

    return NextResponse.json({ error: 'Failed to create warehouse' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedWarehouse = await request.json();

    if (!updatedWarehouse.id) {
      return NextResponse.json(
        { message: 'Warehouse ID is required' },
        { status: 400 },
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if warehouse exists
      const existingWarehouse = await tx.warehouse.findUnique({
        where: { id: updatedWarehouse.id }
      });

      if (!existingWarehouse) {
        throw new Error('Warehouse not found');
      }

      // التحقق من عدم وجود مستودع آخر بنفس الاسم الجديد
      if (updatedWarehouse.name && updatedWarehouse.name !== existingWarehouse.name) {
        const nameExists = await tx.warehouse.findFirst({
          where: {
            name: updatedWarehouse.name,
            id: { not: updatedWarehouse.id }
          }
        });

        if (nameExists) {
          throw new Error('Warehouse name already exists');
        }
      }

      // Update warehouse
      const warehouse = await tx.warehouse.update({
        where: { id: updatedWarehouse.id },
        data: {
          name: updatedWarehouse.name || existingWarehouse.name,
          type: updatedWarehouse.type || existingWarehouse.type,
          location: updatedWarehouse.location !== undefined ? updatedWarehouse.location : existingWarehouse.location,
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated warehouse: ${warehouse.name}`
      });

      return warehouse;
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to update warehouse:', error);

    if (error instanceof Error) {
      if (error.message === 'Warehouse not found') {
        return NextResponse.json({ message: 'Warehouse not found' }, { status: 404 });
      }
      if (error.message === 'Warehouse name already exists') {
        return NextResponse.json({ error: 'Warehouse name already exists' }, { status: 409 });
      }
    }

    return NextResponse.json({ error: 'Failed to update warehouse' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { message: 'Warehouse ID is required' },
        { status: 400 },
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if warehouse exists
      const existingWarehouse = await tx.warehouse.findUnique({
        where: { id }
      });

      if (!existingWarehouse) {
        throw new Error('Warehouse not found');
      }

      // فحص العلاقات الشامل قبل الحذف
      const relatedOperations: string[] = [];

      // فحص الأجهزة في هذا المخزن
      const devicesInWarehouse = await tx.device.count({
        where: { warehouseId: id }
      });
      if (devicesInWarehouse > 0) {
        relatedOperations.push(`${devicesInWarehouse} جهاز`);
      }

      // فحص المبيعات من هذا المخزن
      const relatedSales = await tx.sale.count({
        where: { warehouseName: existingWarehouse.name }
      });
      if (relatedSales > 0) {
        relatedOperations.push(`${relatedSales} فاتورة مبيعات`);
      }

      // فحص المرتجعات من هذا المخزن
      const relatedReturns = await tx.return?.count({
        where: { warehouseName: existingWarehouse.name }
      }) || 0;
      if (relatedReturns > 0) {
        relatedOperations.push(`${relatedReturns} أمر مرتجع`);
      }

      // فحص أوامر التوريد لهذا المخزن
      const relatedSupplyOrders = await tx.supplyOrder?.count({
        where: { warehouseId: id }
      }) || 0;
      if (relatedSupplyOrders > 0) {
        relatedOperations.push(`${relatedSupplyOrders} أمر توريد`);
      }

      if (relatedOperations.length > 0) {
        throw new Error(`Cannot delete warehouse: ${relatedOperations.join(', ')}`);
      }

      // Delete warehouse
      await tx.warehouse.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: parseInt(authResult.user!.id),
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted warehouse: ${existingWarehouse.name}`
      });

      return { message: 'Warehouse deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete warehouse:', error);

    if (error instanceof Error) {
      if (error.message === 'Warehouse not found') {
        return NextResponse.json({ message: 'Warehouse not found' }, { status: 404 });
      }
      if (error.message.startsWith('Cannot delete warehouse:')) {
        const relatedOperations = error.message.replace('Cannot delete warehouse: ', '').split(', ');
        return NextResponse.json({
          error: 'Cannot delete warehouse',
          reason: 'يوجد عمليات أو أجهزة مرتبطة بهذا المخزن',
          relatedOperations
        }, { status: 409 });
      }
    }

    return NextResponse.json({ error: 'Failed to delete warehouse' }, { status: 500 });
  }
}
