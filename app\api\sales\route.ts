import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { requireAuth } from '@/lib/auth';
import { executeInTransaction, createAuditLogInTransaction, generateUniqueId } from '@/lib/transaction-utils';

export async function GET(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const sales = await prisma.sale.findMany({
      orderBy: { id: 'desc' }
    });

    // تحويل حقول JSON من strings إلى objects
    const processedSales = sales.map((sale: any) => ({
      ...sale,
      items: sale.items ?
        (typeof sale.items === 'string' ?
          JSON.parse(sale.items) : sale.items) : [],
      attachments: sale.attachments ?
        (typeof sale.attachments === 'string' ?
          JSON.parse(sale.attachments) : sale.attachments) : null,
    }));

    return NextResponse.json(processedSales);
  } catch (error) {
    console.error('Failed to fetch sales:', error);
    return NextResponse.json({ error: 'Failed to fetch sales' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const newSale = await request.json();

    // Basic validation
    if (!newSale.clientName || !newSale.items || !Array.isArray(newSale.items) || newSale.items.length === 0) {
      return NextResponse.json(
        { error: 'Client name and items are required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Generate unique SO number
      const soNumber = await generateUniqueId(tx, 'sale', 'SO-');

      // ✅ إنشاء opNumber إذا لم يكن موجوداً
      const opNumber = newSale.opNumber || soNumber; // استخدام soNumber كقيمة افتراضية

      // Create the sale in the database
      const sale = await tx.sale.create({
        data: {
          soNumber,
          opNumber, // ✅ استخدام القيمة المولدة
          date: newSale.date,
          clientName: newSale.clientName,
          warehouseName: newSale.warehouseName || 'المخزن الرئيسي',
          items: typeof newSale.items === 'object' ? JSON.stringify(newSale.items) : newSale.items,
          notes: newSale.notes || '',
          warrantyPeriod: newSale.warrantyPeriod || 'none',
          employeeName: newSale.employeeName || authResult.user!.username,
          attachments: newSale.attachments ? JSON.stringify(newSale.attachments) : '[]'
        }
      });

      // Update device statuses
      if (newSale.items && Array.isArray(newSale.items)) {
        for (const item of newSale.items) {
          if (item.deviceId) {
            // التحقق من وجود الجهاز أولاً
            const device = await tx.device.findUnique({
              where: { id: item.deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: item.deviceId },
                data: { status: 'مباع' }
              });
            } else {
              console.warn(`Device ${item.deviceId} not found for sale ${sale.soNumber}`);
            }
          }
        }
      }

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'CREATE',
        details: `Created sale: ${sale.soNumber} for client ${sale.clientName}`,
        tableName: 'sale',
        recordId: sale.id.toString()
      });

      return sale;
    });

    // معالجة حقول JSON قبل الإرسال
    const processedSale = {
      ...result,
      items: result.items ?
        (typeof result.items === 'string' ?
          JSON.parse(result.items) : result.items) : [],
      attachments: result.attachments ?
        (typeof result.attachments === 'string' ?
          JSON.parse(result.attachments) : result.attachments) : null,
    };

    return NextResponse.json(processedSale, { status: 201 });
  } catch (error) {
    console.error('Failed to create sale:', error);
    return NextResponse.json({ error: 'Failed to create sale' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // التحقق من التفويض
    const authResult = await requireAuth(request, 'user');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const updatedSale = await request.json();

    if (!updatedSale.id) {
      return NextResponse.json(
        { error: 'Sale ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if sale exists
      const existingSale = await tx.sale.findUnique({
        where: { id: updatedSale.id }
      });

      if (!existingSale) {
        throw new Error('Sale not found');
      }

      // Get the old items to check for removed devices
      let oldItems = [];
      try {
        oldItems = typeof existingSale.items === 'string' ?
          JSON.parse(existingSale.items) : existingSale.items || [];
      } catch (error) {
        console.warn('Failed to parse old items:', error);
      }

      const newItems = updatedSale.items || [];
      const newItemIds = newItems.map((item: any) => item.deviceId).filter(Boolean);
      const oldItemIds = oldItems.map((item: any) => item.deviceId).filter(Boolean);

      // Find devices that were removed from the sale
      const removedDeviceIds = oldItemIds.filter((deviceId: string) => !newItemIds.includes(deviceId));

      // Reset status for removed devices
      for (const deviceId of removedDeviceIds) {
        const device = await tx.device.findUnique({
          where: { id: deviceId }
        });

        if (device) {
          await tx.device.update({
            where: { id: deviceId },
            data: { status: 'متاح للبيع' }
          });
        }
      }

      // Set status for new devices
      for (const item of newItems) {
        if (item.deviceId) {
          const device = await tx.device.findUnique({
            where: { id: item.deviceId }
          });

          if (device) {
            await tx.device.update({
              where: { id: item.deviceId },
              data: { status: 'مباع' }
            });
          }
        }
      }

      // Update the sale
      const sale = await tx.sale.update({
        where: { id: updatedSale.id },
        data: {
          opNumber: updatedSale.opNumber || existingSale.opNumber,
          date: updatedSale.date || existingSale.date,
          clientName: updatedSale.clientName || existingSale.clientName,
          warehouseName: updatedSale.warehouseName || existingSale.warehouseName,
          items: typeof updatedSale.items === 'object' ? JSON.stringify(updatedSale.items) : updatedSale.items,
          notes: updatedSale.notes !== undefined ? updatedSale.notes : existingSale.notes,
          warrantyPeriod: updatedSale.warrantyPeriod !== undefined ? updatedSale.warrantyPeriod : existingSale.warrantyPeriod,
          employeeName: updatedSale.employeeName || existingSale.employeeName,
          attachments: updatedSale.attachments ? JSON.stringify(updatedSale.attachments) : existingSale.attachments
        }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'UPDATE',
        details: `Updated sale: ${sale.soNumber} for client ${sale.clientName}`,
        tableName: 'sale',
        recordId: sale.id.toString()
      });

      return sale;
    });

    // معالجة حقول JSON قبل الإرسال
    const processedSale = {
      ...result,
      items: result.items ?
        (typeof result.items === 'string' ?
          JSON.parse(result.items) : result.items) : [],
      attachments: result.attachments ?
        (typeof result.attachments === 'string' ?
          JSON.parse(result.attachments) : result.attachments) : null,
    };

    return NextResponse.json(processedSale);
  } catch (error) {
    console.error('Failed to update sale:', error);

    if (error instanceof Error && error.message === 'Sale not found') {
      return NextResponse.json({ error: 'Sale not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to update sale' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // التحقق من التفويض - يتطلب صلاحيات إدارية للحذف
    const authResult = await requireAuth(request, 'manager');
    if (!authResult.success) {
      return NextResponse.json(
        { error: authResult.error },
        { status: authResult.error === 'Insufficient permissions' ? 403 : 401 }
      );
    }

    const { id } = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Sale ID is required' },
        { status: 400 }
      );
    }

    // تنفيذ العملية داخل معاملة
    const result = await executeInTransaction(async (tx) => {
      // Check if sale exists
      const existingSale = await tx.sale.findUnique({
        where: { id }
      });

      if (!existingSale) {
        throw new Error('Sale not found');
      }

      // Reset device statuses
      let items = [];
      try {
        items = typeof existingSale.items === 'string' ?
          JSON.parse(existingSale.items) : existingSale.items || [];
      } catch (error) {
        console.warn('Failed to parse items for device status reset:', error);
      }

      if (Array.isArray(items)) {
        for (const item of items) {
          if (item.deviceId) {
            const device = await tx.device.findUnique({
              where: { id: item.deviceId }
            });

            if (device) {
              await tx.device.update({
                where: { id: item.deviceId },
                data: { status: 'متاح للبيع' }
              });
            }
          }
        }
      }

      // Delete the sale
      await tx.sale.delete({
        where: { id }
      });

      // إنشاء audit log
      await createAuditLogInTransaction(tx, {
        userId: authResult.user!.id,
        username: authResult.user!.username,
        operation: 'DELETE',
        details: `Deleted sale: ${existingSale.soNumber} for client ${existingSale.clientName}`,
        tableName: 'sale',
        recordId: id.toString()
      });

      return { message: 'Sale deleted successfully' };
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('Failed to delete sale:', error);

    if (error instanceof Error && error.message === 'Sale not found') {
      return NextResponse.json({ error: 'Sale not found' }, { status: 404 });
    }

    return NextResponse.json({ error: 'Failed to delete sale' }, { status: 500 });
  }
}
